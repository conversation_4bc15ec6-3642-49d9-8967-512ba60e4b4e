"""
测试 extract_single_tone_information_vvi 函数

测试模块路径：`tests.test_extract_single_tone_information_vvi`

本模块测试单频信息提取函数的各种功能和边界情况。
"""

import pytest
import numpy as np
from sweeper400.analyze import (
    sine_wave_vvi,
    init_sampling_info,
    extract_single_tone_information_vvi,
    Waveform,
)


class TestExtractSingleToneInformationVvi:
    """测试 extract_single_tone_information_vvi 函数"""

    def test_basic_extraction_perfect_sine(self):
        """测试基本的单频信息提取 - 完美正弦波"""
        # 生成测试参数
        sampling_rate = 1000.0
        samples_num = 2048
        # 使用能精确匹配FFT频率bin的频率，以获得最佳相位精度
        freq_resolution = sampling_rate / samples_num
        test_frequency = freq_resolution * 100  # 第100个频率bin，约48.828Hz
        test_amplitude = 2.5
        test_phase = np.pi / 4  # 45度

        # 生成测试波形
        sampling_info = init_sampling_info(int(sampling_rate), samples_num)
        test_waveform = sine_wave_vvi(
            sampling_info, test_frequency, test_amplitude, test_phase
        )

        # 提取单频信息
        detected_freq, detected_amp, detected_phase = (
            extract_single_tone_information_vvi(
                test_waveform, approx_freq=test_frequency
            )
        )

        # 验证结果精度
        assert (
            abs(detected_freq - test_frequency) < 0.01
        ), f"频率误差过大: {detected_freq} vs {test_frequency}"
        assert (
            abs(detected_amp - test_amplitude) < 0.01
        ), f"幅值误差过大: {detected_amp} vs {test_amplitude}"
        # 相位可能有2π的整数倍差异，需要归一化比较
        phase_diff = abs(detected_phase - test_phase)
        phase_diff = min(phase_diff, 2 * np.pi - phase_diff)
        assert phase_diff < 0.01, f"相位误差过大: {detected_phase} vs {test_phase}"

    def test_full_range_search(self):
        """测试全频率范围搜索"""
        # 生成测试参数
        sampling_rate = 2000.0
        samples_num = 4096
        test_frequency = 123.45  # 非整数频率
        test_amplitude = 1.8

        # 生成测试波形
        sampling_info = init_sampling_info(int(sampling_rate), samples_num)
        test_waveform = sine_wave_vvi(sampling_info, test_frequency, test_amplitude)

        # 全频率范围搜索（不指定 approx_freq）
        detected_freq, detected_amp, detected_phase = (
            extract_single_tone_information_vvi(test_waveform, approx_freq=None)
        )

        # 验证结果
        assert (
            abs(detected_freq - test_frequency) < 0.5
        ), f"频率误差过大: {detected_freq} vs {test_frequency}"
        assert (
            abs(detected_amp - test_amplitude) < 0.2
        ), f"幅值误差过大: {detected_amp} vs {test_amplitude}"

    def test_frequency_range_search(self):
        """测试指定频率范围搜索"""
        # 生成测试参数
        sampling_rate = 1000.0
        samples_num = 1024
        test_frequency = 100.0
        test_amplitude = 3.0
        error_percentage = 10.0

        # 生成测试波形
        sampling_info = init_sampling_info(int(sampling_rate), samples_num)
        test_waveform = sine_wave_vvi(sampling_info, test_frequency, test_amplitude)

        # 指定频率范围搜索
        detected_freq, detected_amp, detected_phase = (
            extract_single_tone_information_vvi(
                test_waveform,
                approx_freq=test_frequency,
                error_percentage=error_percentage,
            )
        )

        # 验证结果
        assert (
            abs(detected_freq - test_frequency) < 1.0
        ), f"频率误差过大: {detected_freq} vs {test_frequency}"
        assert (
            abs(detected_amp - test_amplitude) < 0.3
        ), f"幅值误差过大: {detected_amp} vs {test_amplitude}"

    def test_multi_channel_waveform(self):
        """测试多通道波形处理"""
        # 生成多通道测试数据
        sampling_rate = 1000.0
        samples_num = 1024
        test_frequency = 75.0
        test_amplitude = 1.5

        # 生成单通道波形
        sampling_info = init_sampling_info(int(sampling_rate), samples_num)
        single_channel_wave = sine_wave_vvi(
            sampling_info, test_frequency, test_amplitude
        )

        # 创建多通道波形（2通道）
        multi_channel_data = np.array(
            [
                single_channel_wave,  # 第一通道：目标信号
                np.random.normal(0, 0.1, samples_num),  # 第二通道：噪声
            ]
        )
        multi_channel_wave = Waveform(
            multi_channel_data, sampling_rate, single_channel_wave.timestamp
        )

        # 提取单频信息（应该使用第一个通道）
        detected_freq, detected_amp, detected_phase = (
            extract_single_tone_information_vvi(
                multi_channel_wave, approx_freq=test_frequency
            )
        )

        # 验证结果
        assert (
            abs(detected_freq - test_frequency) < 1.0
        ), f"频率误差过大: {detected_freq} vs {test_frequency}"
        assert (
            abs(detected_amp - test_amplitude) < 0.5
        ), f"幅值误差过大: {detected_amp} vs {test_amplitude}"

    def test_noisy_signal(self):
        """测试含噪声信号的处理"""
        # 生成测试参数
        sampling_rate = 2000.0
        samples_num = 4096  # 更多采样点提高抗噪性能
        test_frequency = 200.0
        test_amplitude = 2.0
        noise_level = 0.2

        # 生成测试波形
        sampling_info = init_sampling_info(int(sampling_rate), samples_num)
        clean_waveform = sine_wave_vvi(sampling_info, test_frequency, test_amplitude)

        # 添加噪声
        noise = np.random.normal(0, noise_level, samples_num)
        noisy_data = clean_waveform + noise
        noisy_waveform = Waveform(noisy_data, sampling_rate, clean_waveform.timestamp)

        # 提取单频信息
        detected_freq, detected_amp, detected_phase = (
            extract_single_tone_information_vvi(
                noisy_waveform, approx_freq=test_frequency
            )
        )

        # 验证结果（噪声情况下精度要求放宽）
        assert (
            abs(detected_freq - test_frequency) < 2.0
        ), f"频率误差过大: {detected_freq} vs {test_frequency}"
        assert (
            abs(detected_amp - test_amplitude) < 0.5
        ), f"幅值误差过大: {detected_amp} vs {test_amplitude}"

    def test_edge_cases(self):
        """测试边界情况"""
        # 生成测试参数
        sampling_rate = 1000.0
        samples_num = 512
        test_frequency = 400.0  # 接近奈奎斯特频率
        test_amplitude = 0.5

        # 生成测试波形
        sampling_info = init_sampling_info(int(sampling_rate), samples_num)
        test_waveform = sine_wave_vvi(sampling_info, test_frequency, test_amplitude)

        # 提取单频信息
        detected_freq, detected_amp, detected_phase = (
            extract_single_tone_information_vvi(
                test_waveform, approx_freq=test_frequency
            )
        )

        # 验证结果
        assert (
            abs(detected_freq - test_frequency) < 5.0
        ), f"频率误差过大: {detected_freq} vs {test_frequency}"
        assert detected_amp > 0, "检测到的幅值应该为正数"

    def test_invalid_frequency_range(self):
        """测试无效频率范围的错误处理"""
        # 生成测试参数
        sampling_rate = 1000.0
        samples_num = 1024
        test_frequency = 50.0
        test_amplitude = 1.0

        # 生成测试波形
        sampling_info = init_sampling_info(int(sampling_rate), samples_num)
        test_waveform = sine_wave_vvi(sampling_info, test_frequency, test_amplitude)

        # 测试超出奈奎斯特频率的搜索范围
        with pytest.raises(ValueError, match="指定的频率范围.*超出了有效范围"):
            extract_single_tone_information_vvi(
                test_waveform, approx_freq=600.0, error_percentage=1.0
            )

    def test_different_error_percentages(self):
        """测试不同的误差百分比设置"""
        # 生成测试参数
        sampling_rate = 1000.0
        samples_num = 2048
        test_frequency = 150.0
        test_amplitude = 1.5

        # 生成测试波形
        sampling_info = init_sampling_info(int(sampling_rate), samples_num)
        test_waveform = sine_wave_vvi(sampling_info, test_frequency, test_amplitude)

        # 测试不同的误差百分比
        for error_pct in [1.0, 5.0, 10.0, 20.0]:
            detected_freq, detected_amp, detected_phase = (
                extract_single_tone_information_vvi(
                    test_waveform,
                    approx_freq=test_frequency,
                    error_percentage=error_pct,
                )
            )

            # 验证结果
            assert (
                abs(detected_freq - test_frequency) < 2.0
            ), f"频率误差过大 (error_pct={error_pct}): {detected_freq} vs {test_frequency}"
            assert (
                abs(detected_amp - test_amplitude) < 0.3
            ), f"幅值误差过大 (error_pct={error_pct}): {detected_amp} vs {test_amplitude}"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
