"""
测试环回同步性能

本测试文件用于验证同步AI-AO任务的传输函数一致性。
通过多次测量同一信号，分析接收信号与发送信号之间的相位、频率、幅值关系。
"""

import pytest
import numpy as np
import time
from typing import List, Tuple
from sweeper400.analyze import init_sampling_info
from sweeper400.measure import synchronized_ai_ao_measurement


def test_loopback_synchronization_consistency():
    """
    测试环回同步的一致性
    
    该测试多次执行同步AI-AO测量，验证传输函数的恒定性。
    注意：需要将Slot2的ai0和ao0物理连接。
    """
    print("\n=== 测试环回同步一致性 ===")
    print("注意：此测试需要将Slot2的ai0和ao0物理连接")
    
    # 测试参数
    ai_channel = "402Dev2Slot2/ai0"
    ao_channel = "402Dev2Slot2/ao0"
    
    # 采样参数
    sampling_rate = 10000  # 10 kHz，提高采样率以获得更好的频率分辨率
    samples_num = 8192     # 8192个样本，提供良好的频率分辨率
    sampling_info = init_sampling_info(sampling_rate, samples_num)
    
    # 测试信号参数
    test_frequencies = [100.0, 200.0, 500.0]  # 测试多个频率
    test_amplitude = 2.0  # 2V幅值，足够大以获得良好的信噪比
    test_phase = np.pi / 6  # 30度相位
    
    print(f"测试参数:")
    print(f"  - 采样率: {sampling_rate} Hz")
    print(f"  - 采样数: {samples_num}")
    print(f"  - 测试频率: {test_frequencies} Hz")
    print(f"  - 信号幅值: {test_amplitude} V")
    print(f"  - 信号相位: {test_phase:.4f} rad ({np.degrees(test_phase):.1f}°)")
    print(f"  - 通道: {ai_channel} <- {ao_channel}")
    
    # 对每个频率进行多次测量
    num_measurements = 5  # 每个频率测量5次
    
    for freq in test_frequencies:
        print(f"\n--- 测试频率: {freq} Hz ---")
        
        measurements: List[Tuple[float, float, float]] = []
        
        for i in range(num_measurements):
            print(f"第 {i+1}/{num_measurements} 次测量...")
            
            try:
                # 执行同步测量
                detected_freq, detected_amp, detected_phase = synchronized_ai_ao_measurement(
                    ai_channel=ai_channel,
                    ao_channel=ao_channel,
                    sampling_info=sampling_info,
                    frequency=freq,
                    amplitude=test_amplitude,
                    phase=test_phase
                )
                
                measurements.append((detected_freq, detected_amp, detected_phase))
                
                print(f"  结果: {detected_freq:.2f} Hz, {detected_amp:.4f} V, {detected_phase:.4f} rad")
                
                # 测量间隔，避免硬件状态影响
                time.sleep(0.1)
                
            except Exception as e:
                pytest.fail(f"第 {i+1} 次测量失败: {e}")
        
        # 分析测量结果
        print(f"\n分析 {freq} Hz 的测量结果:")
        
        # 提取各项数据
        freqs = [m[0] for m in measurements]
        amps = [m[1] for m in measurements]
        phases = [m[2] for m in measurements]
        
        # 计算统计信息
        freq_mean = np.mean(freqs)
        freq_std = np.std(freqs)
        amp_mean = np.mean(amps)
        amp_std = np.std(amps)
        phase_mean = np.mean(phases)
        phase_std = np.std(phases)
        
        print(f"频率统计: 均值={freq_mean:.2f} Hz, 标准差={freq_std:.3f} Hz")
        print(f"幅值统计: 均值={amp_mean:.4f} V, 标准差={amp_std:.5f} V")
        print(f"相位统计: 均值={phase_mean:.4f} rad, 标准差={phase_std:.5f} rad")
        
        # 计算传输函数
        amplitude_ratio = amp_mean / test_amplitude
        phase_difference = phase_mean - test_phase
        
        # 将相位差归一化到[-π, π]范围
        phase_difference = np.arctan2(np.sin(phase_difference), np.cos(phase_difference))
        
        print(f"传输函数:")
        print(f"  - 幅值比: {amplitude_ratio:.4f} ({amplitude_ratio*100:.1f}%)")
        print(f"  - 相位差: {phase_difference:.4f} rad ({np.degrees(phase_difference):.1f}°)")
        
        # 验证一致性
        freq_consistency = freq_std < 1.0  # 频率标准差应小于1 Hz
        amp_consistency = (amp_std / amp_mean) < 0.05 if amp_mean > 0 else True  # 幅值相对标准差应小于5%
        phase_consistency = phase_std < 0.1  # 相位标准差应小于0.1弧度（约5.7度）
        
        print(f"一致性检查:")
        print(f"  - 频率一致性: {'✓' if freq_consistency else '✗'} (标准差 {freq_std:.3f} Hz)")
        print(f"  - 幅值一致性: {'✓' if amp_consistency else '✗'} (相对标准差 {(amp_std/amp_mean)*100:.2f}%)" if amp_mean > 0 else "  - 幅值一致性: ✗ (信号太弱)")
        print(f"  - 相位一致性: {'✓' if phase_consistency else '✗'} (标准差 {phase_std:.5f} rad)")
        
        # 如果相位一致性不好，可能表明同步有问题
        if not phase_consistency:
            print(f"⚠ 警告: {freq} Hz 频率下相位一致性较差，可能存在同步问题")
            print("详细相位数据:", [f"{p:.4f}" for p in phases])
        
        # 验证基本功能
        assert freq_consistency, f"{freq} Hz频率检测不一致"
        if amp_mean > 0.01:  # 只有当信号足够强时才检查幅值一致性
            assert amp_consistency, f"{freq} Hz幅值测量不一致"


def test_loopback_transfer_function_analysis():
    """
    测试环回传输函数分析
    
    该测试分析不同频率下的传输函数特性。
    """
    print("\n=== 测试传输函数特性 ===")
    
    # 测试参数
    ai_channel = "402Dev2Slot2/ai0"
    ao_channel = "402Dev2Slot2/ao0"
    sampling_info = init_sampling_info(20000, 16384)  # 更高采样率和更多样本
    
    # 测试多个频率点
    test_frequencies = [50.0, 100.0, 200.0, 500.0, 1000.0, 2000.0]
    test_amplitude = 3.0
    test_phase = 0.0  # 使用0相位便于分析
    
    print(f"分析参数:")
    print(f"  - 测试频率: {test_frequencies} Hz")
    print(f"  - 信号幅值: {test_amplitude} V")
    print(f"  - 信号相位: {test_phase} rad")
    
    transfer_functions = []
    
    for freq in test_frequencies:
        print(f"\n测试频率: {freq} Hz")
        
        try:
            detected_freq, detected_amp, detected_phase = synchronized_ai_ao_measurement(
                ai_channel=ai_channel,
                ao_channel=ao_channel,
                sampling_info=sampling_info,
                frequency=freq,
                amplitude=test_amplitude,
                phase=test_phase
            )
            
            # 计算传输函数
            amplitude_ratio = detected_amp / test_amplitude if test_amplitude > 0 else 0
            phase_difference = detected_phase - test_phase
            phase_difference = np.arctan2(np.sin(phase_difference), np.cos(phase_difference))
            
            transfer_functions.append({
                'frequency': freq,
                'detected_freq': detected_freq,
                'amplitude_ratio': amplitude_ratio,
                'phase_difference': phase_difference,
                'detected_amp': detected_amp,
                'detected_phase': detected_phase
            })
            
            print(f"  检测: {detected_freq:.2f} Hz, {detected_amp:.4f} V, {detected_phase:.4f} rad")
            print(f"  传输: 幅值比={amplitude_ratio:.4f}, 相位差={phase_difference:.4f} rad ({np.degrees(phase_difference):.1f}°)")
            
        except Exception as e:
            print(f"  ✗ 频率 {freq} Hz 测试失败: {e}")
    
    # 分析传输函数特性
    if transfer_functions:
        print(f"\n=== 传输函数总结 ===")
        print("频率(Hz)  | 幅值比   | 相位差(°) | 检测频率(Hz)")
        print("-" * 50)
        
        for tf in transfer_functions:
            print(f"{tf['frequency']:8.1f} | {tf['amplitude_ratio']:8.4f} | {np.degrees(tf['phase_difference']):9.1f} | {tf['detected_freq']:11.2f}")
        
        # 检查传输函数的频率响应特性
        amp_ratios = [tf['amplitude_ratio'] for tf in transfer_functions if tf['amplitude_ratio'] > 0]
        if amp_ratios:
            amp_ratio_mean = np.mean(amp_ratios)
            amp_ratio_std = np.std(amp_ratios)
            print(f"\n幅值传输特性:")
            print(f"  - 平均幅值比: {amp_ratio_mean:.4f}")
            print(f"  - 幅值比标准差: {amp_ratio_std:.5f}")
            print(f"  - 幅值比变化: {(amp_ratio_std/amp_ratio_mean)*100:.2f}%")


if __name__ == "__main__":
    """
    直接运行测试（不使用pytest）
    """
    print("开始测试环回同步性能...")
    print("注意：需要将Slot2的ai0和ao0物理连接")
    
    try:
        test_loopback_synchronization_consistency()
        test_loopback_transfer_function_analysis()
        print("\n=== 所有环回同步测试完成 ===")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        raise
